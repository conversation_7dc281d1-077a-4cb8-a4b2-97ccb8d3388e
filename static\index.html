<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禅道任务查看器</title>
    <!-- SheetJS CDN for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #e8f4fd 0%, #b3d9f2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 12px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-content {
            display: flex;
            gap: 12px;
            flex: 1;
        }

        .left-panel {
            flex: 0 0 350px;
            min-width: 300px;
        }

        .right-panel {
            flex: 1;
            min-width: 0;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 4px;
        }

        .app-subtitle {
            text-align: center;
            color: #666;
            font-size: 13px;
            margin-bottom: 0;
        }
        .config-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            border-radius: 2px;
        }

        .form-grid {
            display: grid;
            gap: 12px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }

        .form-label .required {
            color: #ff4757;
            margin-left: 4px;
        }

        .form-input {
            padding: 8px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .dynamic-fields {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .dynamic-field {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .dynamic-field input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            background: #fff;
        }

        .btn-add {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            color: #6c757d;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .btn-remove {
            padding: 6px 12px;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-remove:hover {
            background: #ff3742;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-top: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            min-width: 100px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .message {
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            margin: 16px 0;
            border: 1px solid;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        .tasks-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex: 1;
        }

        .date-group {
            margin-bottom: 24px;
        }

        .date-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .task-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .task-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .task-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        .task-id {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .task-id:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .task-status {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .task-status.in-progress {
            background: #d1ecf1;
            color: #0c5460;
        }

        .task-time {
            background: #f8f9fa;
            color: #6c757d;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-title {
            font-size: 14px;
            color: #333;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .parent-info {
            background: #f8f9fa;
            color: #6c757d;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            display: inline-block;
        }

        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel {
                flex: none;
                min-width: auto;
            }

            .right-panel {
                flex: none;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                padding: 12px;
            }

            .header, .config-section, .tasks-section {
                padding: 16px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .task-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .main-content {
                gap: 12px;
            }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 图标样式 */
        .icon {
            width: 16px;
            height: 16px;
            stroke: currentColor;
            stroke-width: 2;
            fill: none;
        }

        /* 表格样式 */
        .tasks-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-top: 8px;
        }

        .tasks-table th,
        .tasks-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .tasks-table th {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .tasks-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .tasks-table tbody tr:last-child td {
            border-bottom: none;
        }

        .task-id-cell {
            font-weight: 600;
            color: #2196F3;
        }

        .task-id-cell a {
            color: inherit;
            text-decoration: none;
        }

        .task-id-cell a:hover {
            text-decoration: underline;
        }

        .task-status-cell .task-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-status-cell .task-status.completed {
            background-color: #d4edda;
            color: #155724;
        }

        .task-status-cell .task-status.in-progress {
            background-color: #fff3cd;
            color: #856404;
        }

        .task-time-cell {
            color: #666;
            font-size: 14px;
        }

        .task-date-cell {
            color: #666;
            font-size: 14px;
        }

        .parent-task-cell {
            color: #888;
            font-size: 13px;
        }

        .table-container {
            max-height: calc(100vh - 200px);
            overflow-y: auto;
            border-radius: 12px;
        }

        .no-tasks {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            font-size: 16px;
        }

        .no-tasks i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header Section -->
        <div class="header">
            <h1 class="app-title">禅道任务查看器</h1>
            <p class="app-subtitle">现代化的禅道任务管理工具</p>
        </div>

        <!-- Main Content with Left and Right Panels -->
        <div class="main-content">
            <!-- Left Panel: Configuration Section -->
            <div class="left-panel">
                <div class="config-section">
                    <div class="section-title">
                        <i data-feather="settings"></i>
                        配置设置
                    </div>

                    <div class="form-grid">
                        <!-- 必填的zentaosid字段 -->
                        <div class="form-group">
                            <label class="form-label" for="zentaosidInput">
                                禅道会话ID (zentaosid) <span class="required">*</span>
                            </label>
                            <input type="text" id="zentaosidInput" class="form-input"
                                   placeholder="请输入zentaosid的值，例如：abc123def456..." required>
                        </div>

                        <!-- 可选的额外Cookie字段 -->
                        <div class="form-group">
                            <label class="form-label">额外Cookie字段 (可选)</label>
                            <div class="dynamic-fields" id="extraCookiesContainer">
                                <!-- 动态添加的Cookie字段将在这里显示 -->
                            </div>
                            <button type="button" id="addCookieBtn" class="btn-add">
                                <i data-feather="plus"></i>
                                添加Cookie字段
                            </button>
                        </div>

                        <!-- 可选的额外请求头字段 -->
                        <div class="form-group">
                            <label class="form-label">额外请求头字段 (可选)</label>
                            <div class="dynamic-fields" id="extraHeadersContainer">
                                <!-- 动态添加的请求头字段将在这里显示 -->
                            </div>
                            <button type="button" id="addHeaderBtn" class="btn-add">
                                <i data-feather="plus"></i>
                                添加请求头字段
                            </button>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button id="fetchButton" class="btn btn-primary" disabled>
                            <i data-feather="download"></i>
                            获取任务
                        </button>
                        <button id="downloadExcelButton" class="btn btn-secondary" disabled>
                            <i data-feather="file-text"></i>
                            下载 Excel
                        </button>
                    </div>

                    <div id="message" class="message hidden"></div>
                </div>
            </div>

            <!-- Right Panel: Tasks Section -->
            <div class="right-panel">
                <div class="tasks-section">
                    <div class="section-title">
                        <i data-feather="list"></i>
                        任务列表
                    </div>
                    <div id="tasksContainer"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化Feather图标
            feather.replace();
            const zentaosidInput = document.getElementById('zentaosidInput');
            const fetchButton = document.getElementById('fetchButton');
            const downloadExcelButton = document.getElementById('downloadExcelButton');
            const tasksContainer = document.getElementById('tasksContainer');
            const messageDiv = document.getElementById('message');
            const addCookieBtn = document.getElementById('addCookieBtn');
            const addHeaderBtn = document.getElementById('addHeaderBtn');
            const extraCookiesContainer = document.getElementById('extraCookiesContainer');
            const extraHeadersContainer = document.getElementById('extraHeadersContainer');

            let currentTasks = []; // Cache for tasks
            let zenTaoTaskViewURL = ''; // Global variable to store TASK_VIEW_URL

            // Fetch TASK_VIEW_URL from backend
            fetch('/api/config')
                .then(response => response.json())
                .then(config => {
                    if (config.taskViewURL) {
                        // Remove any query parameters from the URL
                        try {
                            const url = new URL(config.taskViewURL);
                            zenTaoTaskViewURL = url.origin + url.pathname; 
                        } catch (e) {
                            console.warn('Invalid TASK_VIEW_URL format, using as is:', config.taskViewURL);
                            zenTaoTaskViewURL = config.taskViewURL;
                        }
                        console.log('Fetched TASK_VIEW_URL:', zenTaoTaskViewURL);
                    } else {
                        console.warn('TASK_VIEW_URL not found in backend config.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching config:', error);
                });

            // 动态字段管理函数
            function addDynamicField(container, type, name = '', value = '') {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'dynamic-field';

                fieldDiv.innerHTML = `
                    <input type="text" class="field-name" placeholder="${type === 'cookie' ? 'Cookie名称' : '请求头名称'}" value="${name}">
                    <input type="text" class="field-value" placeholder="${type === 'cookie' ? 'Cookie值' : '请求头值'}" value="${value}">
                    <button type="button" class="btn-remove">
                        <i data-feather="x"></i>
                        删除
                    </button>
                `;

                // 添加删除按钮事件
                fieldDiv.querySelector('.btn-remove').addEventListener('click', () => {
                    fieldDiv.remove();
                    saveConfiguration();
                });

                // 添加输入变化事件
                fieldDiv.querySelectorAll('input').forEach(input => {
                    input.addEventListener('input', saveConfiguration);
                });

                container.appendChild(fieldDiv);

                // 重新初始化图标
                feather.replace();

                return fieldDiv;
            }

            // 保存配置到localStorage
            function saveConfiguration() {
                const config = {
                    zentaosid: zentaosidInput.value,
                    extraCookies: [],
                    extraHeaders: []
                };

                // 收集额外Cookie
                extraCookiesContainer.querySelectorAll('.dynamic-field').forEach(field => {
                    const name = field.querySelector('.field-name').value.trim();
                    const value = field.querySelector('.field-value').value.trim();
                    if (name && value) {
                        config.extraCookies.push({ name, value });
                    }
                });

                // 收集额外请求头
                extraHeadersContainer.querySelectorAll('.dynamic-field').forEach(field => {
                    const name = field.querySelector('.field-name').value.trim();
                    const value = field.querySelector('.field-value').value.trim();
                    if (name && value) {
                        config.extraHeaders.push({ name, value });
                    }
                });

                localStorage.setItem('zenTaoConfig', JSON.stringify(config));
            }

            // 从localStorage加载配置
            function loadConfiguration() {
                const savedConfig = localStorage.getItem('zenTaoConfig');
                if (savedConfig) {
                    try {
                        const config = JSON.parse(savedConfig);
                        zentaosidInput.value = config.zentaosid || '';

                        // 加载额外Cookie
                        if (config.extraCookies) {
                            config.extraCookies.forEach(cookie => {
                                addDynamicField(extraCookiesContainer, 'cookie', cookie.name, cookie.value);
                            });
                        }

                        // 加载额外请求头
                        if (config.extraHeaders) {
                            config.extraHeaders.forEach(header => {
                                addDynamicField(extraHeadersContainer, 'header', header.name, header.value);
                            });
                        }
                    } catch (e) {
                        console.error('加载配置失败:', e);
                    }
                }
            }

            // 构建完整的Cookie字符串
            function buildCookieString() {
                let cookieString = `zentaosid=${zentaosidInput.value.trim()}`;

                extraCookiesContainer.querySelectorAll('.dynamic-field').forEach(field => {
                    const name = field.querySelector('.field-name').value.trim();
                    const value = field.querySelector('.field-value').value.trim();
                    if (name && value) {
                        cookieString += `; ${name}=${value}`;
                    }
                });

                return cookieString;
            }

            // 事件监听器
            addCookieBtn.addEventListener('click', () => {
                addDynamicField(extraCookiesContainer, 'cookie');
                saveConfiguration();
            });

            addHeaderBtn.addEventListener('click', () => {
                addDynamicField(extraHeadersContainer, 'header');
                saveConfiguration();
            });

            zentaosidInput.addEventListener('input', () => {
                saveConfiguration();
                updateButtonStates();
            });

            // 按钮状态管理函数
            function updateButtonStates() {
                const hasZentaosid = zentaosidInput.value.trim().length > 0;
                const hasTasks = currentTasks.length > 0;

                // 获取任务按钮：有zentaosid且不在加载中时启用
                fetchButton.disabled = !hasZentaosid || fetchButton.classList.contains('loading');

                // 下载Excel按钮：有任务时启用
                downloadExcelButton.disabled = !hasTasks;
            }

            // 设置加载状态
            function setLoadingState(button, isLoading, loadingText = '') {
                if (isLoading) {
                    button.classList.add('loading');
                    button.disabled = true;
                    if (loadingText) {
                        button.dataset.originalText = button.textContent;
                        button.textContent = loadingText;
                    }
                } else {
                    button.classList.remove('loading');
                    if (button.dataset.originalText) {
                        button.textContent = button.dataset.originalText;
                        delete button.dataset.originalText;
                    }
                    updateButtonStates(); // 恢复正常状态管理
                }
            }

            // 加载保存的配置
            loadConfiguration();

            // 初始化按钮状态
            updateButtonStates();

            fetchButton.addEventListener('click', async () => {
                const zentaosidValue = zentaosidInput.value.trim();
                if (!zentaosidValue) {
                    showMessage('zentaosid 不能为空！', 'error');
                    return;
                }

                // 设置加载状态
                setLoadingState(fetchButton, true, '获取中...');

                // 构建完整的Cookie字符串
                const cookieValue = buildCookieString();

                // 保存配置
                saveConfiguration();

                showMessage('正在获取任务，请稍候...', 'info');
                tasksContainer.innerHTML = ''; // Clear previous tasks
                currentTasks = []; // Clear cached tasks
                updateButtonStates(); // 更新按钮状态

                try {
                    // 构建请求数据，包含Cookie和额外请求头
                    const requestData = {
                        cookie: cookieValue,
                        extraHeaders: {}
                    };

                    // 收集额外请求头
                    extraHeadersContainer.querySelectorAll('.dynamic-field').forEach(field => {
                        const name = field.querySelector('.field-name').value.trim();
                        const value = field.querySelector('.field-value').value.trim();
                        if (name && value) {
                            requestData.extraHeaders[name] = value;
                        }
                    });

                    const response = await fetch('/api/tasks', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData),
                    });

                    const data = await response.json();
                    console.log('Backend response data:', data); // For debugging

                    if (response.ok) {
                        if (data.tasks && data.tasks.length > 0) {
                            showMessage(`成功获取 ${data.tasks.length} 个任务！`, 'success');
                            currentTasks = data.tasks; // Cache the fetched tasks

                            // 按日期分组任务
                            const tasksByDate = data.tasks.reduce((acc, task) => {
                                const date = task.date || '无日期';
                                if (!acc[date]) {
                                    acc[date] = [];
                                }
                                acc[date].push(task);
                                return acc;
                            }, {});

                            // 按日期排序（最新的在前）
                            const sortedDates = Object.keys(tasksByDate).sort((a, b) => {
                                return b.localeCompare(a);
                            });

                            // 为每个日期创建表格
                            sortedDates.forEach(date => {
                                // 创建日期标题
                                const dateHeader = document.createElement('div');
                                dateHeader.className = 'date-header';
                                dateHeader.style.cssText = `
                                    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                                    color: white;
                                    padding: 8px 16px;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    font-weight: 600;
                                    margin: 16px 0 8px 0;
                                    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
                                    display: flex;
                                    align-items: center;
                                    gap: 6px;
                                `;
                                dateHeader.innerHTML = `
                                    <i data-feather="calendar"></i>
                                    ${date} (${tasksByDate[date].length}个任务)
                                `;
                                tasksContainer.appendChild(dateHeader);

                                // 创建该日期的表格容器
                                const tableContainer = document.createElement('div');
                                tableContainer.className = 'table-container';
                                tableContainer.style.marginBottom = '16px';

                                // 创建表格
                                const table = document.createElement('table');
                                table.className = 'tasks-table';

                                // 创建表头
                                const thead = document.createElement('thead');
                                thead.innerHTML = `
                                    <tr>
                                        <th>任务ID</th>
                                        <th>标题</th>
                                        <th>状态</th>
                                        <th>耗时</th>
                                        <th>父任务</th>
                                    </tr>
                                `;
                                table.appendChild(thead);

                                // 创建表体
                                const tbody = document.createElement('tbody');

                                tasksByDate[date].forEach(task => {
                                    const row = document.createElement('tr');

                                    // 构建任务详情链接
                                    const taskDetailLink = zenTaoTaskViewURL ?
                                        `${zenTaoTaskViewURL.replace('%s', task.id)}` :
                                        `#`;

                                    // 构建状态显示
                                    let statusHtml = '';
                                    if (task.status) {
                                        const statusClass = task.status === '完成了' ? 'completed' : 'in-progress';
                                        statusHtml = `<span class="task-status ${statusClass}">${task.status}</span>`;
                                    }

                                    // 构建耗时显示
                                    let timeText = '';
                                    if (task.timeSpent && task.timeSpent !== '未知' && task.timeSpent !== '0小时') {
                                        timeText = task.timeSpent;
                                    }

                                    // 构建父任务显示
                                    let parentText = '';
                                    if (task.isSubTask && task.parentTitle) {
                                        parentText = task.parentTitle;
                                    }

                                    row.innerHTML = `
                                        <td class="task-id-cell">
                                            <a href="${taskDetailLink}" target="_blank">#${task.id}</a>
                                        </td>
                                        <td class="task-title-cell">${task.title}</td>
                                        <td class="task-status-cell">${statusHtml}</td>
                                        <td class="task-time-cell">${timeText}</td>
                                        <td class="parent-task-cell">${parentText}</td>
                                    `;

                                    tbody.appendChild(row);
                                });

                                table.appendChild(tbody);
                                tableContainer.appendChild(table);
                                tasksContainer.appendChild(tableContainer);
                            });

                            // 重新初始化图标
                            feather.replace();

                        } else {
                            showMessage('没有找到任务，请检查 Cookie 或禅道配置。', 'info');
                            currentTasks = []; // Clear cached tasks if no results

                            // 显示空状态
                            const noTasksDiv = document.createElement('div');
                            noTasksDiv.className = 'no-tasks';
                            noTasksDiv.innerHTML = `
                                <i data-feather="inbox"></i>
                                <div>暂无任务数据</div>
                                <div style="font-size: 14px; margin-top: 8px;">请检查配置或重新获取任务</div>
                            `;
                            tasksContainer.appendChild(noTasksDiv);
                            feather.replace();
                        }
                    } else {
                        const errorMessage = data.error || '未知错误';
                        showMessage(`获取任务失败: ${errorMessage}`, 'error');
                        console.error('API Error:', data.details || data.error);
                        currentTasks = []; // Clear cached tasks on error
                    }
                } catch (error) {
                    showMessage(`请求失败: ${error.message}`, 'error');
                    console.error('Fetch Error:', error);
                    currentTasks = []; // Clear cached tasks on error
                } finally {
                    // 恢复按钮状态
                    setLoadingState(fetchButton, false);
                }
            });

            downloadExcelButton.addEventListener('click', () => {
                if (currentTasks.length === 0) {
                    showMessage('没有可下载的任务，请先点击“获取任务”！', 'error');
                    return;
                }

                showMessage('正在生成 Excel 文件...', 'info');

                // Prepare data for Excel
                const worksheetData = [
                    ["工单编号", "标题", "是否子任务", "父任务标题", "日期", "状态", "耗时"]
                ];

                currentTasks.forEach(task => {
                    const isSubTaskText = task.isSubTask ? '是' : '否';
                    worksheetData.push([
                        task.id,
                        task.title,
                        isSubTaskText,
                        task.parentTitle || '',
                        task.date || '',
                        task.status || '',
                        task.timeSpent || ''
                    ]);
                });

                const ws = XLSX.utils.aoa_to_sheet(worksheetData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "禅道任务");

                // Save the file
                XLSX.writeFile(wb, "禅道任务.xlsx");

                showMessage('Excel 文件已成功下载！', 'success');
            });

            function showMessage(msg, type) {
                messageDiv.textContent = msg;
                messageDiv.className = `message ${type}`;
                messageDiv.classList.remove('hidden');

                // 自动隐藏消息
                setTimeout(() => {
                    messageDiv.classList.add('hidden');
                }, 5000);
            }
        });
    </script>
</body>
</html>